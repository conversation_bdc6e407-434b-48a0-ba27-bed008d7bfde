<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { ElMessageBox, ElNotification } from 'element-plus';

import {
  getMaterialListByFG,
  insertMcodeByFg,
} from '#/api/production/material';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { TableAction } from '#/components/table-action';
import { $t } from '#/locales';

const fgCode = ref('');

const gridRef = ref<InstanceType<typeof ClientGridComponent> | null>(null);
const gridRef2 = ref<InstanceType<typeof ClientGridComponent> | null>(null);

const selectedRows = ref<any[]>([]);

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      fgCode.value = modalApi.getData()?.fgCode || {};
      selectedRows.value = [];
      getData();
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    ElMessageBox.confirm('确定要保存当前输入数据吗?', 'Warning', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        saveData();
      })
      .catch(() => {
        ElNotification({
          duration: 2500,
          message: $t('production.CancelSuccess'),
          type: 'info',
        });
      });
  },
});
async function getData(data: [] | any = []) {
  // 获取selectRows中的id，组装成string,用,隔开
  const ids = selectedRows.value.map((row) => row.id).join(',');
  const res = await getMaterialListByFG({
    ...data,
    ids,
    fgCode: fgCode.value,
  });
  rowData.value = res;
}
const [QueryForm] = useVbenForm({
  handleSubmit: (values) => {
    getData(values);
  },
  submitButtonOptions: {
    content: $t('page.base.search'),
  },
  resetButtonOptions: {
    content: $t('page.base.reset'),
  },
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  collapsed: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  schema: [
    {
      label: $t('production.RawMaterialCode'),
      component: 'Input',
      fieldName: 'mCode',
    },
    {
      label: $t('production.RawMaterialName'),
      component: 'Input',
      fieldName: 'mName',
    },
  ],
});

const columnDefs: any[] = [
  { headerName: $t('production.RawMaterialCode'), field: 'mCode', width: 127 },
  { headerName: $t('production.RawMaterialName'), field: 'mName', width: 127 },
  { headerName: $t('production.ProdCode'), field: 'productCode', width: 112 },
  { headerName: $t('production.ProdName'), field: 'productName', width: 112 },
  { headerName: $t('production.Unit'), field: 'unit', width: 90 },
  { headerName: $t('production.SpecModel'), field: 'specModel', width: 112 },
  { headerName: $t('production.InternalNo'), field: 'intCode', width: 112 },
];
const columnDefs2: any[] = [
  { headerName: $t('production.RawMaterialCode'), field: 'mCode', width: 127 },
  { headerName: $t('production.RawMaterialName'), field: 'mName', width: 127 },
  { headerName: $t('production.ProdCode'), field: 'productCode', width: 112 },
  { headerName: $t('production.ProdName'), field: 'productName', width: 112 },
  { headerName: $t('production.Unit'), field: 'unit', width: 90 },
  { headerName: $t('production.SpecModel'), field: 'specModel', width: 112 },
  { headerName: $t('production.InternalNo'), field: 'intCode', width: 112 },
  // { headerName: $t('production.MatItemNo'), field: 'matItemNo' },
  // { headerName: $t('production.OriginCountry'), field: 'originCountry' },
  // { headerName: $t('production.UnitPrice'), field: 'unitPrice' },
  // { headerName: $t('production.Width'), field: 'width' },
  // { headerName: $t('production.GSM'), field: 'gsm' },
  // { headerName: $t('production.HSUpdater'), field: 'hsUpdater' },
  // { headerName: $t('production.HSTimeUpdated'), field: 'hsTimeUpdated' },
  // { headerName: $t('production.HSReviewer'), field: 'hsReviewer' },
  // { headerName: $t('production.HSTimeReviewed'), field: 'hsTimeReviewed' },
  // { headerName: $t('production.Obsolete'), field: 'obsolete' },
];
const defaultColDef: any = {
  // flex: 1
};
const rowSelection = ref({
  mode: 'multiRow' as 'multiRow', // singleRow 默认单选//multiRow
  checkboxes: true, // 默认关闭
  headerCheckbox: true, // 默认关闭
  copySelectedRows: true,
});
const rowData = ref<any[]>([]);

// 获取选中的行，将选中的行保存到selectedRows中
const successRows = function (data: any) {
  // 获取选中的行
  const gridRows = gridRef.value?.gridApi?.getSelectedRows();
  // 删除当前选中行
  if (gridRows) {
    // 使用 id 进行删除（假设每行数据都有一个 id）
    const selectedIds = new Set(gridRows.map((row) => row.id));
    // 从数据源 `selectedRows` 里删除选中的行
    rowData.value = rowData.value.filter((row) => !selectedIds.has(row.id));
    selectedRows.value = [...selectedRows.value, ...gridRows];
  }
};

const deleteRows = function (data: any) {
  // 获取选中的行
  const gridRows = gridRef2.value?.gridApi?.getSelectedRows();
  // 清除选中的行并更新gridRef
  if (gridRows) {
    // 使用 id 进行删除（假设每行数据都有一个 id）
    const selectedIds = new Set(gridRows.map((row) => row.id));
    // 从数据源 `selectedRows` 里删除选中的行
    selectedRows.value = selectedRows.value.filter(
      (row) => !selectedIds.has(row.id),
    );
    rowData.value = [...rowData.value, ...gridRows];
  }
};

const saveData = async function () {
  // 判断列表中是否存在数据

  // 获取到selectedRows中的id，以及Mcode,组装成stringJson
  const data = selectedRows.value.map((row) => ({
    id: row.id,
    mCode: row.mCode,
  }));
  if (selectedRows.value.length === 0) {
    modalApi.close();
    return;
  }
  await insertMcodeByFg({
    items: JSON.stringify(data),
    fgCode: fgCode.value,
  });

  ElNotification({
    duration: 2500,
    message: $t('production.SaveSuccess'),
    type: 'success',
  });
  modalApi.close();
};

const title = $t('production.Material');
defineExpose(modalApi);
</script>
<template>
  <Modal class="w-[1000px]" :title="title">
    <QueryForm />
    <div style="height: 30vh; margin-top: 20px">
      <ClientGridComponent
        ref="gridRef"
        :column-defs="columnDefs"
        :row-data="rowData"
        :page-size="20"
        height="30vh"
        :default-col-def="defaultColDef"
        :row-selection="rowSelection"
      />
    </div>
    <div style="margin-top: 10px; text-align: center">
      <TableAction
        :actions="[
          {
            type: 'success',
            icon: 'ep:bottom',
            onClick: (data: any) => {
              successRows(data);
            },
          },
          {
            type: 'danger',
            icon: 'ep:top',
            onClick: (data: any) => {
              deleteRows(data);
            },
          },
        ]"
      />
    </div>
    <div style="height: 25vh; margin-top: 10px">
      <ClientGridComponent
        ref="gridRef2"
        :column-defs="columnDefs2"
        :row-data="selectedRows"
        :page-size="20"
        height="25vh"
        :default-col-def="defaultColDef"
        :row-selection="rowSelection"
      />
    </div>
  </Modal>
</template>
