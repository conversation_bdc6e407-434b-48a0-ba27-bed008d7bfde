<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { PageCard } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import { ElMessageBox, ElNotification } from 'element-plus';

import {
  cancelConfirmPurchaseOrder,
  confirmPurchaseOrder,
  deletePurchase,
  getImportedList,
} from '#/api/purchase/imported';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { useMessage } from '#/components/elementPlus/useMessage';
import { TableAction } from '#/components/table-action';
import { $t } from '#/locales';

import ImportedDetail from './components/ImportedDetail.vue';
import ImportImported from './components/ImportImported.vue';
import QueryForm from './components/QueryForm.vue';

/**
 * 组件引用
 * importedDetailRef: 详情弹窗组件引用
 * importImportedRef: 导入弹窗组件引用
 * gridRef: 表格组件引用
 */
const importedDetailRef = ref();
const importImportedRef = ref();
const gridRef = ref<any>(null);

/**
 * 用户信息
 */
const userStore = useUserStore();

/**
 * 数据状态
 * rowData: 表格数据
 * searchParams: 搜索参数
 */
const rowData = ref<any[]>([]);
const searchParams = ref({});

/**
 * Grid配置选项
 * 包含onGridReady回调，确保在Grid准备就绪时调用getData
 */
const gridOptions = {
  onGridReady: (_params: any) => {
    // Grid准备就绪后获取数据
    getData();
  },
};

/**
 * 表格列定义
 * 定义表格的列结构，包括列标题、字段名、排序和筛选等配置
 */
const columnDefs: any[] = [
  {
    headerName: $t('purchase.importOrderNo'),
    field: 'orderNo',
  },
  {
    headerName: $t('purchase.orderDate'),
    field: 'orderDate',
  },
  {
    headerName: $t('purchase.orderStatus'),
    field: 'orderStatus',
    valueFormatter: (params: any) => {
      const statusMap: Record<string, string> = {
        new: $t('purchase.new'),
        confirm: $t('purchase.confirm'),
        store: $t('purchase.store'),
      };
      return statusMap[params.value] || params.value;
    },
  },
  {
    headerName: $t('purchase.poNo'),
    field: 'poNo',
  },
  {
    headerName: $t('purchase.declarationNo'),
    field: 'declarationNo',
  },
  {
    headerName: $t('purchase.supplier'),
    field: 'supplier',
  },
  {
    headerName: $t('purchase.receiptDate'),
    field: 'receiptDate',
  },
  {
    headerName: $t('purchase.confirmUserName'),
    field: 'confirmUserName',
  },
  {
    headerName: $t('purchase.confirmDateTime'),
    field: 'confirmDateTime',
  },
  {
    headerName: $t('production.Action'),
    field: 'action', // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    // flex: 1,
    cellRendererParams: {
      actions: {
        label: '...',
        primaryActions: [
          {
            label: $t('basic.view'),
            callback: (data: any) => {
              handleEdit(data, 'view');
            },
            auth: ['purchase.imported.view'],
            type: 'primary',
            size: 'small',
          },
          {
            label: $t('purchase.confirmOrder'),
            callback: (data: any) => {
              handleConfirmOrder(data);
            },
            auth: ['purchase.imported.confirm'],
            type: 'success',
            size: 'small',
            show: (data: any) => data.orderStatus === 'new', // 只有新建状态才显示确认按钮
          },
          {
            label: $t('purchase.cancelConfirm'),
            callback: (data: any) => {
              handleCancelConfirm(data);
            },
            auth: ['purchase.imported.cancelConfirm'],
            type: 'warning',
            size: 'small',
            show: (data: any) => data.orderStatus === 'confirm', // 只有确认状态才显示取消确认按钮
          },
        ],
        menuItems: [
          {
            label: $t('basic.delete'),
            callback: (data: any) => {
              handleDelete(data);
            },
            auth: ['purchase.imported.delete'],
            type: 'danger',
            size: 'small',
          },
        ],
      },
    },
  },
];

/**
 * 获取国外进口数据列表
 * 根据搜索参数从服务器获取数据并更新表格
 */
const getData = async (data: [] | any = []) => {
  try {
    const res = await getImportedList({ ...data, orderType: 'import' });
    if (Array.isArray(res)) {
      rowData.value = res;

      // 延迟执行 autoSizeAllColumns，确保数据已经渲染到DOM
      setTimeout(() => {
        if (gridRef.value?.gridApi) {
          gridRef.value.gridApi.autoSizeAllColumns();
        }
      }, 10);
    }
  } catch {
    useMessage().showMessage('error', $t('basic.fetchFailed'));
  }
};

/**
 * 处理编辑或查看操作
 * 打开详情弹窗，根据pageType设置为编辑或查看模式
 * @param data 当前行数据
 * @param pageType 页面类型，'edit'为编辑，'view'为查看
 */
const handleEdit = (data: any, pageType: string) => {
  importedDetailRef.value.modalApi.setData({
    record: data,
    isUpdate: pageType === 'edit',
    isView: pageType === 'view',
  });
  importedDetailRef.value.modalApi.open();
  // 关闭后刷新数据
  importedDetailRef.value.modalApi.setState({
    onClosed: () => {
      getData();
    },
  });
};

/**
 * 处理删除操作
 * 弹出确认框，确认后删除数据
 * @param data 要删除的数据行
 */
const handleDelete = async (data: any) => {
  try {
    // 弹出确认对话框
    await ElMessageBox.confirm($t('basic.confirmDelete'), $t('basic.tips'), {
      confirmButtonText: $t('basic.confirm'),
      cancelButtonText: $t('basic.cancel'),
      type: 'warning',
    });

    // 调用删除API
    await deletePurchase({ id: data.id });
    ElNotification({
      duration: 2500,
      message: $t('basic.deleteSuccess'),
      type: 'success',
    });
    getData(); // 删除成功后刷新数据
  } catch (error) {
    console.error($t('basic.deleteFailed'), error);
  }
};

/**
 * 处理导入操作
 * 打开导入弹窗
 */
const handleImport = () => {
  importImportedRef.value.modalApi.open();
  // 关闭后刷新数据
  importImportedRef.value.modalApi.setState({
    onClosed: () => {
      getData();
    },
  });
};

/**
 * 处理确认订单操作
 * 将订单状态从"新建"更改为"确认"
 * @param data 要确认的订单数据
 */
const handleConfirmOrder = async (data: any) => {
  try {
    // 弹出确认对话框
    await ElMessageBox.confirm(
      $t('purchase.confirmOrderTip'),
      $t('basic.tips'),
      {
        confirmButtonText: $t('basic.confirm'),
        cancelButtonText: $t('basic.cancel'),
        type: 'warning',
      },
    );

    // 调用确认订单API，传入确认人和确认时间
    await confirmPurchaseOrder({
      id: data.id,
      confirmUserName:
        userStore.userInfo?.realName ||
        userStore.userInfo?.username ||
        '未知用户',
      confirmDateTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      }),
    });
    ElNotification({
      duration: 2500,
      message: $t('purchase.confirmOrderSuccess'),
      type: 'success',
    });
    getData(); // 确认成功后刷新数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error($t('purchase.confirmOrderFailed'), error);
      ElNotification({
        duration: 2500,
        message: $t('purchase.confirmOrderFailed'),
        type: 'error',
      });
    }
  }
};

/**
 * 处理取消确认订单操作
 * 将订单状态从"确认"更改回"新建"
 * @param data 要取消确认的订单数据
 */
const handleCancelConfirm = async (data: any) => {
  try {
    // 弹出确认对话框
    await ElMessageBox.confirm(
      $t('purchase.cancelConfirmTip'),
      $t('basic.tips'),
      {
        confirmButtonText: $t('basic.confirm'),
        cancelButtonText: $t('basic.cancel'),
        type: 'warning',
      },
    );

    // 调用取消确认订单API
    await cancelConfirmPurchaseOrder({ id: data.id });
    ElNotification({
      duration: 2500,
      message: $t('purchase.cancelConfirmSuccess'),
      type: 'success',
    });
    getData(); // 取消确认成功后刷新数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error($t('purchase.cancelConfirmFailed'), error);
      ElNotification({
        duration: 2500,
        message: $t('purchase.cancelConfirmFailed'),
        type: 'error',
      });
    }
  }
};

/**
 * 处理搜索操作
 * 更新搜索参数并重新获取数据
 * @param formData 表单数据
 */
const handleSearch = (formData: any) => {
  getData(formData);
};

/**
 * 处理重置操作
 * 清空搜索参数并重新获取数据
 */
const handleReset = () => {
  searchParams.value = {};
  getData();
};

/**
 * 组件挂载时执行
 * 注意：不在这里调用getData()，而是等待Grid准备就绪后自动调用
 */
onMounted(() => {
  // Grid准备就绪后会自动调用getData()
});
</script>

<template>
  <PageCard auto-content-height>
    <!-- 查询表单组件 -->
    <QueryForm @search="handleSearch" @reset="handleReset" />

    <!-- 操作按钮区域 -->
    <TableAction
      :actions="[
        {
          label: $t('basic.import'),
          type: 'primary',
          icon: 'ep:upload',
          auth: ['purchase.imported.add'],
          onClick: handleImport.bind(null),
        },
      ]"
    />

    <!-- 数据表格 -->
    <ClientGridComponent
      ref="gridRef"
      :column-defs="columnDefs"
      :row-data="rowData"
      :grid-options="gridOptions"
    />

    <!-- 详情弹窗组件 -->
    <ImportedDetail ref="importedDetailRef" />

    <!-- 导入弹窗组件 -->
    <ImportImported ref="importImportedRef" />
  </PageCard>
</template>
