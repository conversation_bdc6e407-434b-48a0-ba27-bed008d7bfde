<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  addOrEditMaterialDeclaration,
  getProductDeclaration,
  getProductDecMataial,
} from '#/api/production/productDeclaration';
import ClientGridComponent from '#/components/ag-grid/ClientGridComponent.vue';
import { useMessage } from '#/components/elementPlus/useMessage';
import { TableAction } from '#/components/table-action';
import { $t } from '#/locales';

const title = ref($t('product.DeclarationDetail'));
const hsCode = ref('');
const mCode = ref('');
const productName = ref('');
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false,
  draggable: true,
  onOpenChange(isOpen) {
    if (isOpen) {
      this.loading = true;
      hsCode.value = modalApi.getData()?.hsCode;
      mCode.value = modalApi.getData()?.mCode;
      productName.value = modalApi.getData()?.productName;
      getData();
      this.loading = false;
      title.value = `${$t('product.DeclarationDetail')}【${mCode.value}___${
        hsCode.value
      }】`;
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    useMessage().showMessageBox(
      'confirm',
      $t('basic.confirmSave'),
      $t('basic.tips'),
      'warning',
      async (action) => {
        if (action === 'confirm') {
          // 结束当前编辑
          gridRef.value?.gridApi.stopEditing();
          // 直接从网格获取所有数据
          const allData: any[] = [];
          gridRef.value?.gridApi.forEachNode((node: any) => {
            allData.push(node.data);
          });

          // ✅ 校验：标记为必填的行，content 不能为空
          const invalidRows = allData.filter(
            (row) => row.isMust && !row.content?.trim(),
          );

          if (invalidRows.length > 0) {
            useMessage().showMessageBox(
              'alert',
              ` ${invalidRows.length} ${$t('product.confirmDelaration')}`,
            );
            return;
          }
          // 按照序号排序
          allData.sort((a, b) => a.seq - b.seq);

          // 按declaration分组并组合内容
          const groupedData = allData.reduce((acc, item) => {
            if (!acc[item.declaration]) {
              acc[item.declaration] = [];
            }
            if (item.content) {
              acc[item.declaration].push(item.content);
            }
            return acc;
          }, {});

          // 组合成最终格式：名称：内容1,内容2,内容3
          const formattedData = Object.entries(groupedData)
            .map(([declaration, contents]) => {
              const contentStr = (contents as string[]).join(',');
              return contentStr ? `${declaration}:${contentStr}` : '';
            })
            .filter(Boolean) // 移除空字符串
            .join(',');

          // 检查数据是否有变化
          // const hasChanges = JSON.stringify(allData) !== JSON.stringify(rowData.value);

          // if (hasChanges) {
          await addOrEditMaterialDeclaration({
            hsCode: hsCode.value,
            productName: productName.value,
            mCode: mCode.value,
            items: JSON.stringify(allData),
            specModel: formattedData,
          });
          // useMessage().showMessage('success', $t('production.SaveSuccess'));
          // }

          modalApi.sharedData = {
            payload: formattedData,
          };
          modalApi.close();
        }
      },
    );
  },
});
const gridRef = ref();

const defaultColDef = {
  // flex: 1,
  editable: false,
};

// Configure row selection
const rowSelection = {
  mode: 'multiRow' as 'multiRow',
  checkboxes: true,
  headerCheckbox: true,
  copySelectedRows: true,
};
const columnDefs: any[] = [
  {
    headerName: $t('product.Seq'),
    field: 'seq',
    maxWidth: 100,
    cellEditorParams: {
      min: 1, // 最小值
      precision: 0, // 整数
    },
    valueParser: (params: any) => {
      // 确保输入的是有效数字
      const newValue = Number(params.newValue);
      return isNaN(newValue) ? params.oldValue : newValue;
    },
  },
  // 单独自动调整列
  {
    headerName: $t('product.declaration'),
    field: 'declaration',
    suppressSizeToFit: true,
  },
  {
    headerName: $t('product.Specification'),
    field: 'content',
    width: 135,
    flex: 1,
    cellClassRules: {
      'cell-error': (params: any) => {
        return params.data.isMust && !params.value?.trim();
      },
    },
    editable: true,
  },
  // 增加勾选框列
  {
    headerName: $t('product.isMust'),
    field: 'isMust',
    cellRenderer: 'agCheckboxCellRenderer', // 显示为勾选框
    cellEditor: 'agCheckboxCellEditor', // 编辑也为勾选框
    width: 100,
    cellStyle: { textAlign: 'center' },
    // 把 null / undefined 当作 false 来显示
    valueGetter: (params: any) => {
      return !!params.data?.isMust;
    },

    // 点勾选后写入 true/false（不是 null）
    valueSetter: (params: any) => {
      const newVal = !!params.newValue;
      if (params.data.isMust !== newVal) {
        params.data.isMust = newVal;
        return true;
      }
      return false;
    },
  },
  { headerName: $t('product.deCode'), field: 'code', suppressSizeToFit: true },
  {
    headerName: $t('product.deCodeContetn'),
    field: 'codeContent',
    suppressSizeToFit: true,
  },
];
const rowData = ref<any[]>([]);
async function getData() {
  const res = await getProductDecMataial({
    hsCode: hsCode.value,
    mCode: mCode.value,
  });
  if (Array.isArray(res)) {
    rowData.value = res;
    // 如果没有查到任何数据，则自动添加
    // if (rowData.value.length === 0) {
    //   handleReset();
    // }
  }
}

async function getDataProduct() {
  const res = await getProductDeclaration({
    hsCode: hsCode.value,
  });
  if (Array.isArray(res)) {
    rowData.value = res;
  }
}

defineExpose(modalApi);
</script>
<template>
  <Modal class="h-[60%] w-[70%] min-w-[500px]" :title="title">
    <TableAction
      :actions="[
        {
          label: $t('basic.refresh'),
          type: 'primary',
          icon: 'ep:refresh',
          onClick: getData.bind(null),
        },
        {
          label: $t('basic.reset'),
          type: 'primary',
          icon: 'ep:refresh',
          onClick: getDataProduct.bind(null),
        },
      ]"
    />
    <div style="height: 90%; margin-top: 20px">
      <ClientGridComponent
        ref="gridRef"
        :column-defs="columnDefs"
        :row-data="rowData"
        :page-size="20"
        :default-col-def="defaultColDef"
        :row-selection="rowSelection"
      />
    </div>
  </Modal>
</template>
<style>
.cell-error {
  background-color: #fff0f0 !important;
  border: 1px solid red !important;
}
</style>
