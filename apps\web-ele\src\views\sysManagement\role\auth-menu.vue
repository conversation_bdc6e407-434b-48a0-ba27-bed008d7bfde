<script lang="ts" setup>
import { ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import {
  ElButton as Button,
  ElInput as Input,
  ElMessage as message,
  ElSwitch as Switch,
  ElTree as Tree,
} from 'element-plus';

import { sysRolePerm, sysRolePermEdit } from '#/api/sys/role';
import { $t } from '#/locales';
import { accessRoutes } from '#/router/routes';
import { getAllNodeIds } from '#/util/tool';

interface RouteConfig {
  meta: {
    buttons?: string[]; // 新增 buttons 字段，存储按钮操作
    icon?: string;
    order?: number;
    // affixTab?: boolean;
    // keepAlive?: boolean;
    perms: string[]; // 标记权限码
    title: ((...args: any[]) => string) | string; // 支持国际化函数
    type?: 'api' | 'button' | 'menu'; // 菜单类型
  };
  id?: string;
  isChecked?: boolean;
  name: string;
  // path: string;
  children?: RouteConfig[];
}

const record = ref();
const treeRef = ref();
const treeData = ref();
const isExpand = ref(false);
const filterText = ref('');
const checkStrictly = ref(true);
const loading = ref(false);

// 勾选的key
const checkedKeys = ref<string[]>([]);
// 提交的勾选的key,会进行特殊处理，包含半勾状态的父节点halfCheckedKeys
const submitCheckedPerms = ref<any>([]);

// 所有节点key
const allNodeIds = ref([]);
// 当前展开的key
const currentExpandedKeys = ref([]);

// 监听过滤文本变化，触发树的过滤方法
watch(filterText, (val) => {
  if (treeRef.value) {
    treeRef.value.filter(val);
  }
});

function convertToRouteConfig(
  routes: any[],
  t: (key: string) => string,
  perms: string[],
): RouteConfig[] {
  return routes
    .filter((route) => !route.meta?.ignoreAccess && !route.meta?.hideInMenu)
    .sort((a, b) => (a.meta?.order || 0) - (b.meta?.order || 0))
    .map((route, index) => {
      const sChecked =
        Array.isArray(route.meta.perms) &&
        route.meta.perms.some((perm: string) => perms.includes(perm));
      const routeId = route.name + index; // 这里是简单的基于 `name` 和 `path` 生成唯一标识符
      // 防止 route.meta.buttons 和 route.children 为 undefined
      const children = Array.isArray(route.children)
        ? convertToRouteConfig(route.children, t, perms)
        : [];
      const buttons = Array.isArray(route.meta.buttons)
        ? convertToRouteConfig(route.meta.buttons, t, perms)
        : [];

      return {
        meta: {
          icon: route.meta?.icon || undefined,
          title:
            typeof route.meta.title === 'function'
              ? route.meta.title()
              : t(route.meta.title),
          // buttons: route.meta.buttons || [],
          perms: route.meta.perms,
          type: route.meta.type || 'menu', // 菜单类型，默认为menu
        },
        id: routeId,
        name: route.name,
        isChecked: sChecked, // 判断该路由的权限码是否在传入的权限码列表中
        children: children.length > 0 ? children : buttons, // 没有子路由时显示按钮
      };
    });
}

function updateCheckedKeys(perms: string[]) {
  const routes = treeData.value || [];

  // 递归检查所有节点的 isChecked 状态
  const collectCheckedKeys = (routes: RouteConfig[]): string[] => {
    return routes.reduce((checked: string[], route: RouteConfig) => {
      // 如果当前节点被勾选，添加到结果中
      if (route.isChecked && route.id) {
        checked.push(route.id); // 添加当前节点
      }

      // 递归处理子路由（子级的勾选会影响父级）
      if (route.children && route.children.length > 0) {
        checked.push(...collectCheckedKeys(route.children)); // 展开所有子路由
      }

      return checked;
    }, []);
  };
  submitCheckedPerms.value = perms;
  // 获取所有勾选的节点
  checkedKeys.value = collectCheckedKeys(routes);
  treeRef.value?.setCheckedKeys(checkedKeys.value);
}

const getCheckedPerms = (checkedNodes: any[]) => {
  const checkedPermissions: string[] = [];
  // const checkedPermissions = new Set(); // 使用 Set 来避免重复

  checkedNodes.forEach((node) => {
    // 检查当前节点是否有 perms
    if (node.meta && node.meta.perms) {
      checkedPermissions.push(...node.meta.perms); // 将 perms 添加到 checkedPermissions 数组中
    }

    // 如果有子节点，递归处理子节点
    if (node.children && node.children.length > 0) {
      checkedPermissions.push(...getCheckedPerms(node.children)); // 递归获取子节点的权限
    }
  });

  return checkedPermissions;
};

/**
 * 点击复选框触发处理
 */
const handleCheck = (_node: any, e: any) => {
  const checkedPerms: string[] = [];
  const halfCheckedPerms: string[] = [];

  // 遍历勾选的节点，收集它们的权限
  e.checkedKeys.forEach((key: string) => {
    const node = findNodeByKey(key, treeData.value); // 查找单个节点
    if (node && node.meta && node.meta.perms) {
      checkedPerms.push(...node.meta.perms); // 收集权限
    }
  });

  // 如果不是严格模式，还需要处理半选中状态的节点
  if (!checkStrictly.value && e.halfCheckedKeys) {
    e.halfCheckedKeys.forEach((key: string) => {
      const node = findNodeByKey(key, treeData.value);
      if (node && node.meta && node.meta.perms) {
        halfCheckedPerms.push(...node.meta.perms);
      }
    });
  }

  // 去重并更新
  submitCheckedPerms.value = [
    ...new Set([...checkedPerms, ...halfCheckedPerms]),
  ];
};

const findNodeByKey = (key: string, treeData: any[]): any => {
  // 遍历树数据
  for (const node of treeData) {
    // 如果找到了匹配的节点，返回该节点
    if (node.id === key) {
      // 假设 key 对应的是 node.id
      return node;
    }

    // 如果节点有子节点，则递归查找
    if (node.children && node.children.length > 0) {
      const foundNode = findNodeByKey(key, node.children);
      if (foundNode) {
        return foundNode; // 如果子节点中找到，则返回该节点
      }
    }
  }

  return null; // 如果没有找到，返回 null
};

/**
 * 过滤节点方法
 */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  // 检查节点标题是否包含搜索文本
  return data.meta?.title
    ?.toString()
    .toLowerCase()
    .includes(value.toLowerCase());
};

/**
 * 展开/折叠所有节点
 */
const handleExpandAndCollapse = () => {
  isExpand.value = !isExpand.value;
  if (isExpand.value) {
    // 展开所有节点
    if (treeData.value && treeData.value.length > 0) {
      // 获取所有节点ID
      allNodeIds.value = getAllNodeIds(treeData.value);
      // 设置当前展开的节点
      currentExpandedKeys.value = [...allNodeIds.value];
      // 展开所有节点
      allNodeIds.value.forEach((id: string) => {
        treeRef.value?.store.nodesMap[id]?.expand();
      });
    }
  } else {
    // 折叠所有节点
    if (allNodeIds.value && allNodeIds.value.length > 0) {
      allNodeIds.value.forEach((id: string) => {
        treeRef.value?.store.nodesMap[id]?.collapse();
      });
      // 清空当前展开的节点
      currentExpandedKeys.value = [];
    }
  }
};

/**
 * 切换严格模式
 */
const handleCheckStrictlyChange = (val: boolean) => {
  checkStrictly.value = val;
};

/**
 * 保存权限
 */
const handleSavePermissions = () => {
  loading.value = true;
  sysRolePermEdit({
    roleId: record.value.id,
    perms: submitCheckedPerms.value,
  })
    .then(() => {
      message.success($t('basic.UpdateSuccess'));
      DrawerApi.close();
    })
    .catch((error) => {
      message.error(`保存失败: ${error.message || '未知错误'}`);
    })
    .finally(() => {
      loading.value = false;
    });
};

const [Drawer, DrawerApi] = useVbenDrawer({
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = isOpen ? DrawerApi.getData()?.record : {};
      DrawerApi.setState({
        loading: true,
      });
      loading.value = true;

      // 重置状态
      filterText.value = '';
      isExpand.value = false;

      // 获取授权码
      sysRolePerm({ roleId: record.value.id })
        .then((res) => {
          const routes: RouteConfig[] = convertToRouteConfig(
            accessRoutes,
            $t,
            res,
          );
          treeData.value = routes;
          updateCheckedKeys(res);
        })
        .catch((error) => {
          message.error(`获取权限失败: ${error.message || '未知错误'}`);
        })
        .finally(() => {
          DrawerApi.setState({
            loading: false,
          });
          loading.value = false;
        });
    }
  },
  onConfirm() {
    // 使用Popconfirm组件来确认，这里不直接保存
    // sysRolePermEdit({
    //     roleId: record.value.id,
    //     perms: submitCheckedPerms.value,
    //   }).then((res) => {
    //     message.success('保存成功');
    //   });
    handleSavePermissions();
  },
});
const renderTreeNode = (
  _h: typeof import('vue').h,
  { node: _node, data }: { data: RouteConfig; node: any },
) => {
  const h = _h;
  return h(
    'span',
    { class: 'custom-tree-node', style: 'display: flex; align-items: center;' },
    [
      data.meta.icon
        ? h(IconifyIcon, {
            icon: data.meta.icon,
            style: 'margin-right: 5px; font-size: 1.2em;',
          })
        : null,
      h(
        'span',
        {
          class: {
            'menu-node': data.meta.type === 'menu',
            'button-node': data.meta.type === 'button',
            'api-node': data.meta.type === 'api',
          },
        },
        data.meta.title,
      ),
    ],
  );
};

defineExpose(DrawerApi);
</script>
<template>
  <div>
    <Drawer class="w-[40%]" title="授权菜单">
      <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center">
          <Button
            :disabled="false"
            type="primary"
            @click="handleExpandAndCollapse"
            class="mr-2"
          >
            {{ isExpand ? '折叠' : '展开' }}
          </Button>
          <Input
            v-model="filterText"
            placeholder="输入关键字过滤"
            class="ml-2 w-[200px]"
            clearable
          />
        </div>
        <div class="flex items-center">
          <span class="mr-2">严格模式:</span>
          <Switch
            v-model="checkStrictly"
            @change="(val) => handleCheckStrictlyChange(Boolean(val))"
            active-text="开启"
            inactive-text="关闭"
          />
        </div>
      </div>

      <Tree
        ref="treeRef"
        :data="treeData"
        :props="{
          label: (data) => $t(data.meta.title),
          children: 'children',
        }"
        node-key="id"
        show-checkbox
        v-model:checked-keys="checkedKeys.values"
        @check="handleCheck"
        :render-content="renderTreeNode"
        :check-strictly="checkStrictly"
        :filter-node-method="filterNode"
        default-expand-all
      />

      <!-- <template #footer>
        <div class="flex justify-end">
          <Button @click="DrawerApi.close()" class="mr-2">取消</Button>
          <Popconfirm
            title="确认保存权限设置?"
            confirm-button-text="确定"
            cancel-button-text="取消"
            @confirm="handleSavePermissions"
          >
            <Button type="primary" :loading="loading">保存</Button>
          </Popconfirm>
        </div>
      </template> -->
    </Drawer>
  </div>
</template>

<style scoped>
.menu-node {
  font-weight: bold;
}

.button-node {
  color: #409eff;
}

.api-node {
  color: #67c23a;
}
</style>
