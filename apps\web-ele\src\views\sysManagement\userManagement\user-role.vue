<script lang="ts" setup>
import { ref, watch } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { RotateCw, Search } from '@vben/icons';

import {
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>,
  <PERSON>Divider as Divider,
  ElButton,
  ElInput as Input,
  ElMessage as message,
  ElTree as Tree,
} from 'element-plus';

import { getRoleByUser, insertRoleByUser, sysRoleGet } from '#/api/sys/role';

// 用户记录
const record = ref<null | { id?: number; name?: string }>(null);
// 树引用
const treeRef = ref();
// 树数据
const treeData = ref([]);
// 选中的键
const checkedKeys = ref<number[]>([]);
// 提交的权限
const submitCheckedPerms = ref<number[]>([]);
// 搜索文本
const filterText = ref('');
// 加载状态
const loading = ref(false);
/**
 * 更新选中的键
 * @param ids 选中的ID数组
 */
function updateCheckedKeys(ids: number[]) {
  submitCheckedPerms.value = ids;
  checkedKeys.value = ids;

  // 确保树组件已加载后再设置选中状态
  if (treeRef.value) {
    treeRef.value.setCheckedKeys(checkedKeys.value);
  }
}

/**
 * 抽屉配置
 */
const [Drawer, DrawerApi] = useVbenDrawer({
  closeOnClickModal: false,
  onOpenChange(isOpen) {
    if (isOpen) {
      record.value = DrawerApi.getData()?.record || {};
      DrawerApi.setState({ loading: true });
      loading.value = true;

      // 异步获取角色数据
      fetchRoleData();
    }
  },
  onConfirm() {
    handleSaveRoles();
  },
});

/**
 * 保存角色授权
 */
function handleSaveRoles() {
  if (!record.value?.id) {
    message.error('用户ID不能为空');
    return;
  }

  // 确保 id 是数字类型
  const userId =
    typeof record.value.id === 'string'
      ? Number.parseInt(record.value.id, 10)
      : record.value.id;

  if (isNaN(userId)) {
    message.error('用户ID格式无效');
    return;
  }

  loading.value = true;
  DrawerApi.setState({ submitting: true });

  insertRoleByUser({
    id: userId,
    itemList: submitCheckedPerms.value,
  })
    .then(() => {
      message.success('角色授权保存成功');
      DrawerApi.close();
    })
    .catch((error: any) => {
      console.error('保存角色授权失败', error);
      message.error(`保存失败: ${error.message || '未知错误'}`);
    })
    .finally(() => {
      loading.value = false;
      DrawerApi.setState({ submitting: false });
    });
}

/**
 * 异步请求获取角色数据
 */
const fetchRoleData = async () => {
  loading.value = true;
  DrawerApi.setState({ loading: true });

  try {
    // 获取所有角色
    const roleData = await sysRoleGet({});
    treeData.value = roleData;

    // 确保 id 是数字类型
    const userId =
      typeof record.value?.id === 'string'
        ? Number.parseInt(record.value.id, 10)
        : record.value?.id;

    if (userId && !isNaN(userId)) {
      // 获取用户已有角色
      const userRoles = await getRoleByUser({ id: userId });
      updateCheckedKeys(userRoles); // 更新勾选的角色
    }
  } catch (error: any) {
    console.error('角色数据加载失败', error);
    message.error(`获取角色数据失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
    DrawerApi.setState({ loading: false });
  }
};

/**
 * 点击复选框触发处理
 */
const handleCheck = (_node: any, e: any) => {
  const checkedPerms: number[] = [];

  // 遍历勾选的节点，收集它们的ID
  e.checkedKeys.forEach((key: number | string) => {
    const node = findNodeByKey(key, treeData.value);
    if (node && node.id) {
      checkedPerms.push(node.id);
    }
  });

  // 去重并更新
  submitCheckedPerms.value = [...new Set(checkedPerms)];
};

/**
 * 根据键查找节点
 */
const findNodeByKey = (key: number | string, treeData: any[]) => {
  // 遍历树数据
  for (const node of treeData) {
    // 如果找到了匹配的节点，返回该节点
    if (node.id === key) {
      return node;
    }
  }
  return null; // 如果没有找到，返回 null
};

/**
 * 刷新角色数据
 */
const refreshRoleData = () => {
  fetchRoleData();
};

/**
 * 过滤节点方法
 */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.roleName.toLowerCase().includes(value.toLowerCase());
};

// 监听过滤文本变化
watch(filterText, (val) => {
  treeRef.value?.filter(val);
});

defineExpose(DrawerApi);
</script>

<template>
  <div>
    <Drawer class="w-[40%]" title="授权角色">
      <Alert v-if="record?.name" type="info" class="mb-4">
        当前为用户 <strong>{{ record.name }}</strong> 授权角色
      </Alert>

      <!-- 工具栏 -->
      <div class="mb-4 flex items-center justify-between">
        <!-- 搜索框 -->
        <div class="mr-2 flex-1">
          <Input
            v-model="filterText"
            placeholder="输入关键字搜索角色"
            clearable
            :prefix-icon="Search"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-2">
          <ElButton
            size="small"
            @click="refreshRoleData"
            :loading="loading"
            title="刷新角色列表"
          >
            <RotateCw />
          </ElButton>
        </div>
      </div>

      <Divider content-position="center">角色列表</Divider>

      <!-- 角色树组件 -->
      <Tree
        ref="treeRef"
        :data="treeData"
        :checked-keys="checkedKeys"
        show-checkbox
        node-key="id"
        :props="{
          label: (data) => data.roleName,
          children: 'children',
        }"
        :filter-node-method="filterNode"
        @check="handleCheck"
        default-expand-all
      />

      <!-- 自定义底部按钮 -->
      <!-- <template #footer>
        <div class="flex justify-end">
          <ElButton @click="DrawerApi.close()" class="mr-2">取消</ElButton>
          <ElPopconfirm
            title="确认保存角色授权设置?"
            confirm-button-text="确定"
            cancel-button-text="取消"
            @confirm="handleSaveRoles"
          >
            <template #reference>
              <ElButton type="primary" :loading="loading">保存</ElButton>
            </template>
          </ElPopconfirm>
        </div>
      </template> -->
    </Drawer>
  </div>
</template>

<style scoped>
.el-tree {
  max-height: 400px;
  padding: 10px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
</style>
